import { defineConfig } from 'vite'

export default defineConfig({
  // Ensure environment variables with VITE_ prefix are available in the browser
  define: {
    // Make sure environment variables are properly exposed
    'process.env': {}
  },
  
  // Development server configuration
  server: {
    port: 3000,
    open: true
  },
  
  // Build configuration
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['openai']
        }
      }
    }
  },
  
  // Environment variables configuration
  envPrefix: 'VITE_'
})
