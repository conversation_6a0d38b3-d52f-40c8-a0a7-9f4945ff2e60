// Canvas Renderer Module
// Handles all drawing operations on the HTML5 canvas

import { calculateMarkdownHeight } from './markdownParser.js';
import { renderMarkdownToCanvas } from './markdownRenderer.js';

export function drawGrid(ctx, offsetX, offsetY, scale, canvasWidth, canvasHeight) {
    const gridSize = 40;
    const startX = Math.floor(-offsetX / scale / gridSize) * gridSize;
    const startY = Math.floor(-offsetY / scale / gridSize) * gridSize;
    const endX = startX + (canvasWidth / scale) + gridSize;
    const endY = startY + (canvasHeight / scale) + gridSize;
    
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    ctx.globalAlpha = 0.5;
    
    for (let x = startX; x < endX; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, startY);
        ctx.lineTo(x, endY);
        ctx.stroke();
    }
    
    for (let y = startY; y < endY; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(startX, y);
        ctx.lineTo(endX, y);
        ctx.stroke();
    }
    
    ctx.globalAlpha = 1;
}

export function wrapText(ctx, text, maxWidth) {
    const words = text.split(' ');
    const lines = [];
    let currentLine = '';
    
    words.forEach(word => {
        const testLine = currentLine + (currentLine ? ' ' : '') + word;
        const metrics = ctx.measureText(testLine);
        
        if (metrics.width > maxWidth && currentLine) {
            lines.push(currentLine);
            currentLine = word;
        } else {
            currentLine = testLine;
        }
    });
    
    if (currentLine) {
        lines.push(currentLine);
    }
    
    return lines;
}

export function drawNode(ctx, node, showConnectionPoints = false) {
    const { x, y, width, text, isSelected, generatedByModel, maxHeight = 400, scrollY = 0 } = node;

    // Enhanced margin configuration for better text spacing
    const margins = {
        top: 28,    // Increased top margin for better visual balance and spacing from border
        bottom: 12, // Bottom margin
        left: 12,   // Left margin
        right: 12   // Right margin
    };

    const minHeight = 40;

    // Calculate required height based on markdown content with new margins
    const requiredHeight = Math.max(minHeight, calculateMarkdownHeight(ctx, text, width, margins, node.fontSize || 14));

    // Only auto-adjust height if node hasn't been manually resized, but respect maxHeight
    if (!node.manuallyResized) {
        node.height = Math.min(requiredHeight, maxHeight);
    }

    const height = node.height;

    // Store the full content height for scrolling calculations
    node.contentHeight = requiredHeight;

    // Draw model attribution above the node if it was generated by AI
    if (generatedByModel) {
        ctx.save();
        ctx.font = '14px Arial';
        ctx.fillStyle = '#9ca3af';
        ctx.textAlign = 'center';

        // Position the text above the node
        const modelText = `🤖 ${generatedByModel}`;
        const textX = x + width / 2;
        const textY = y - 10;

        // Draw a small background for better readability
        const textMetrics = ctx.measureText(modelText);
        const textWidth = textMetrics.width;
        const textHeight = 16;

        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(textX - textWidth/2 - 4, textY - textHeight + 2, textWidth + 8, textHeight);

        ctx.fillStyle = '#9ca3af';
        ctx.fillText(modelText, textX, textY);
        ctx.restore();
    }

    // Draw node background
    ctx.fillStyle = isSelected ? '#2d3748' : '#1a202c';
    ctx.fillRect(x, y, width, height);

    // Draw node border
    ctx.strokeStyle = isSelected ? '#4299e1' : '#4a5568';
    ctx.lineWidth = isSelected ? 2 : 1;
    ctx.strokeRect(x, y, width, height);

    // Draw connection points if requested
    if (showConnectionPoints) {
        drawConnectionPoints(ctx, node);
    }

    // Render markdown content with enhanced margins and scrolling support
    renderMarkdownToCanvas(ctx, text, x, y, width, height, margins, node.scrollY || 0, node.fontSize || 14);

    // Draw scroll indicator if content is scrollable and node is selected
    if (node.contentHeight > height && isSelected) {
        drawScrollIndicator(ctx, node);
    }
}

export function drawScrollIndicator(ctx, node) {
    const { x, y, width, height, contentHeight, scrollY = 0 } = node;

    if (contentHeight <= height) return; // No scrolling needed

    // Calculate scrollbar dimensions
    const scrollbarWidth = 8;
    const scrollbarX = x + width - scrollbarWidth - 4;
    const scrollbarY = y + 4;
    const scrollbarHeight = height - 8;

    // Calculate thumb position and size
    const thumbHeight = Math.max(20, (height / contentHeight) * scrollbarHeight);
    const maxScrollY = contentHeight - height;
    const thumbY = scrollbarY + (scrollY / maxScrollY) * (scrollbarHeight - thumbHeight);

    // Draw scrollbar track
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.fillRect(scrollbarX, scrollbarY, scrollbarWidth, scrollbarHeight);

    // Draw scrollbar thumb
    ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
    ctx.fillRect(scrollbarX, thumbY, scrollbarWidth, thumbHeight);
}

export function drawConnectionPoints(ctx, node) {
    const { x, y, width, height } = node;
    const pointRadius = 8;

    // Calculate connection point positions
    const points = getConnectionPoints(node);

    // Draw each connection point as a circle with enhanced styling
    points.forEach(point => {
        // Outer glow effect
        ctx.shadowColor = 'rgba(66, 153, 225, 0.6)';
        ctx.shadowBlur = 8;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // Main circle
        ctx.fillStyle = '#4299e1';
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 3;

        ctx.beginPath();
        ctx.arc(point.x, point.y, pointRadius, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();

        // Inner highlight
        ctx.fillStyle = '#63b3ed';
        ctx.beginPath();
        ctx.arc(point.x, point.y, pointRadius - 3, 0, 2 * Math.PI);
        ctx.fill();

        // Reset shadow
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
    });
}

// Helper function to get connection point coordinates
export function getConnectionPoints(node) {
    const { x, y, width, height } = node;

    return [
        { x: x + width / 2, y: y, side: 'top' },           // Top center
        { x: x + width, y: y + height / 2, side: 'right' }, // Right center
        { x: x + width / 2, y: y + height, side: 'bottom' }, // Bottom center
        { x: x, y: y + height / 2, side: 'left' }          // Left center
    ];
}

// Helper function to check if a point is over a connection point
export function getConnectionPointAt(node, x, y) {
    const points = getConnectionPoints(node);
    const pointRadius = 12; // Slightly larger detection radius for easier clicking

    for (const point of points) {
        const distance = Math.sqrt(Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2));
        if (distance <= pointRadius) {
            return point;
        }
    }
    return null;
}

// Helper function to calculate intersection of line with rectangle
function getLineRectangleIntersection(centerX, centerY, targetX, targetY, rectX, rectY, rectWidth, rectHeight) {
    // Calculate direction vector
    const dx = targetX - centerX;
    const dy = targetY - centerY;

    // If no direction, return center
    if (dx === 0 && dy === 0) {
        return { x: centerX, y: centerY };
    }

    // Calculate intersections with all four edges
    const intersections = [];

    // Left edge (x = rectX)
    if (dx !== 0) {
        const t = (rectX - centerX) / dx;
        const y = centerY + t * dy;
        if (t > 0 && y >= rectY && y <= rectY + rectHeight) {
            intersections.push({ x: rectX, y: y, distance: t });
        }
    }

    // Right edge (x = rectX + rectWidth)
    if (dx !== 0) {
        const t = (rectX + rectWidth - centerX) / dx;
        const y = centerY + t * dy;
        if (t > 0 && y >= rectY && y <= rectY + rectHeight) {
            intersections.push({ x: rectX + rectWidth, y: y, distance: t });
        }
    }

    // Top edge (y = rectY)
    if (dy !== 0) {
        const t = (rectY - centerY) / dy;
        const x = centerX + t * dx;
        if (t > 0 && x >= rectX && x <= rectX + rectWidth) {
            intersections.push({ x: x, y: rectY, distance: t });
        }
    }

    // Bottom edge (y = rectY + rectHeight)
    if (dy !== 0) {
        const t = (rectY + rectHeight - centerY) / dy;
        const x = centerX + t * dx;
        if (t > 0 && x >= rectX && x <= rectX + rectWidth) {
            intersections.push({ x: x, y: rectY + rectHeight, distance: t });
        }
    }

    // Return the closest intersection
    if (intersections.length > 0) {
        const closest = intersections.reduce((min, curr) =>
            curr.distance < min.distance ? curr : min
        );
        return { x: closest.x, y: closest.y };
    }

    // Fallback to center if no intersection found
    return { x: centerX, y: centerY };
}

export function drawConnection(ctx, connection, nodes, isSelected = false) {
    const fromNode = nodes.find(n => n.id === connection.from);
    const toNode = nodes.find(n => n.id === connection.to);

    if (!fromNode || !toNode) return;

    // Calculate connection points (center of nodes)
    const fromCenterX = fromNode.x + fromNode.width / 2;
    const fromCenterY = fromNode.y + fromNode.height / 2;
    const toCenterX = toNode.x + toNode.width / 2;
    const toCenterY = toNode.y + toNode.height / 2;

    // Calculate proper edge intersection points for rectangular nodes
    const fromEdge = getLineRectangleIntersection(
        fromCenterX, fromCenterY, toCenterX, toCenterY,
        fromNode.x, fromNode.y, fromNode.width, fromNode.height
    );

    const toEdge = getLineRectangleIntersection(
        toCenterX, toCenterY, fromCenterX, fromCenterY,
        toNode.x, toNode.y, toNode.width, toNode.height
    );

    // Calculate angle for arrow head
    const angle = Math.atan2(toCenterY - fromCenterY, toCenterX - fromCenterX);

    // Add offset to ensure arrow head is visible outside the node
    const arrowOffset = 18; // Increased offset for better visibility
    const arrowX = toEdge.x - Math.cos(angle) * arrowOffset;
    const arrowY = toEdge.y - Math.sin(angle) * arrowOffset;

    // Draw line with enhanced styling - different colors for selected state
    if (isSelected) {
        ctx.strokeStyle = '#2196f3'; // Blue for selected
        ctx.lineWidth = 4; // Thicker when selected
    } else {
        ctx.strokeStyle = '#e53e3e'; // Red for normal
        ctx.lineWidth = 3;
    }
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Add subtle shadow for depth
    if (isSelected) {
        ctx.shadowColor = 'rgba(33, 150, 243, 0.4)';
    } else {
        ctx.shadowColor = 'rgba(229, 62, 62, 0.3)';
    }
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    ctx.beginPath();
    ctx.moveTo(fromEdge.x, fromEdge.y);
    ctx.lineTo(arrowX, arrowY);
    ctx.stroke();

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // Draw arrowhead at the offset position with selection state
    drawArrowhead(ctx, arrowX, arrowY, angle, isSelected);
}

export function drawArrowhead(ctx, x, y, angle, isSelected = false) {
    const arrowLength = 16; // Increased from 12 for better visibility
    const arrowAngle = Math.PI / 4; // Wider angle (45 degrees) for more prominent arrow

    // Add enhanced shadow for the arrowhead
    if (isSelected) {
        ctx.shadowColor = 'rgba(33, 150, 243, 0.5)';
    } else {
        ctx.shadowColor = 'rgba(229, 62, 62, 0.4)';
    }
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;

    // Enhanced colors for better contrast - different colors for selected state
    if (isSelected) {
        ctx.fillStyle = '#1976d2'; // Blue for selected
        ctx.strokeStyle = '#0d47a1'; // Darker blue border
    } else {
        ctx.fillStyle = '#d63031'; // Slightly darker red for better visibility
        ctx.strokeStyle = '#a71e1e'; // Darker border for definition
    }
    ctx.lineWidth = 2; // Thicker border

    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(
        x - arrowLength * Math.cos(angle - arrowAngle),
        y - arrowLength * Math.sin(angle - arrowAngle)
    );
    ctx.lineTo(
        x - arrowLength * Math.cos(angle + arrowAngle),
        y - arrowLength * Math.sin(angle + arrowAngle)
    );
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
}

export function drawConnectionPreview(ctx, connectionStart, lastMouseX, lastMouseY, offsetX, offsetY, scale, connectionPoint = null) {
    if (!connectionStart) return;

    const canvasX = (lastMouseX - offsetX) / scale;
    const canvasY = (lastMouseY - offsetY) / scale;

    // Use connection point if provided, otherwise use node center
    let fromX, fromY;
    if (connectionPoint) {
        fromX = connectionPoint.x;
        fromY = connectionPoint.y;
    } else {
        fromX = connectionStart.x + connectionStart.width / 2;
        fromY = connectionStart.y + connectionStart.height / 2;
    }

    // Draw dashed preview line
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(canvasX, canvasY);
    ctx.stroke();
    ctx.setLineDash([]);
}

// Helper function to calculate distance from a point to a line segment
function distanceToLineSegment(px, py, x1, y1, x2, y2) {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) {
        // Line segment is actually a point
        return Math.sqrt(A * A + B * B);
    }

    let param = dot / lenSq;

    let xx, yy;

    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy);
}

// Helper function to check if a point is on a connection
export function getConnectionAtPoint(x, y, connections, nodes, tolerance = 8) {
    for (const connection of connections) {
        const fromNode = nodes.find(n => n.id === connection.from);
        const toNode = nodes.find(n => n.id === connection.to);

        if (!fromNode || !toNode) continue;

        // Calculate connection points (center of nodes)
        const fromCenterX = fromNode.x + fromNode.width / 2;
        const fromCenterY = fromNode.y + fromNode.height / 2;
        const toCenterX = toNode.x + toNode.width / 2;
        const toCenterY = toNode.y + toNode.height / 2;

        // Calculate proper edge intersection points for rectangular nodes
        const fromEdge = getLineRectangleIntersection(
            fromCenterX, fromCenterY, toCenterX, toCenterY,
            fromNode.x, fromNode.y, fromNode.width, fromNode.height
        );

        const toEdge = getLineRectangleIntersection(
            toCenterX, toCenterY, fromCenterX, fromCenterY,
            toNode.x, toNode.y, toNode.width, toNode.height
        );

        // Calculate angle for arrow head
        const angle = Math.atan2(toCenterY - fromCenterY, toCenterX - fromCenterX);

        // Add offset to ensure arrow head is visible outside the node
        const arrowOffset = 18;
        const arrowX = toEdge.x - Math.cos(angle) * arrowOffset;
        const arrowY = toEdge.y - Math.sin(angle) * arrowOffset;

        // Check if point is close to the line segment
        const distance = distanceToLineSegment(x, y, fromEdge.x, fromEdge.y, arrowX, arrowY);

        if (distance <= tolerance) {
            return connection;
        }
    }

    return null;
}

export function drawSelectionRectangle(ctx, selectionStart, selectionEnd) {
    if (!selectionStart || !selectionEnd) return;

    const x = Math.min(selectionStart.x, selectionEnd.x);
    const y = Math.min(selectionStart.y, selectionEnd.y);
    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    // Draw selection rectangle background
    ctx.fillStyle = 'rgba(66, 153, 225, 0.1)';
    ctx.fillRect(x, y, width, height);

    // Draw selection rectangle border
    ctx.strokeStyle = '#4299e1';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.strokeRect(x, y, width, height);
    ctx.setLineDash([]);
}