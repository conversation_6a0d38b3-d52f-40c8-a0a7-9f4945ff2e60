# HumOS Canvas - Your Personal AI Integration Framework

**Your Personal Interface for Enhanced AI Collaboration**

![HumOS Canvas Interface](public/image.png)

HumOS Canvas is a modern infinite canvas application with AI-powered idea generation, built with Vite and supporting multiple AI providers.

## Features

- **Infinite Canvas**: Pan, zoom, and navigate a limitless workspace
- **Node Management**: Create, edit, resize, and delete nodes
- **Connections**: Link nodes with visual connections (Ctrl/Cmd + click)
- **AI Integration**: Generate connected ideas using AI models
- **Multi-Provider Support**: Works with OpenAI, OpenRouter, local models, and any OpenAI-compatible API
- **Undo/Redo**: Full history management
- **Auto-save**: Local storage persistence
- **Export**: JSON export functionality

## About HumOS

HumOS (Human-AI Integration Framework) aims to enhance human capabilities, foster self-understanding, and contribute to solving complex challenges through continuous learning and collaboration. The system is designed around the "Synergy Loop" - a continuous cycle of information flow and co-creation between human and AI.

### Icon Design Philosophy

The HumOS icon represents the core philosophy of human-AI integration:

- **Left side (Blue gradient)**: Human consciousness, brain patterns, and cognitive processes
- **Right side (Gold gradient)**: AI neural networks, processing capabilities, and machine learning
- **Central synergy core (White)**: The integration point where human and AI capabilities merge and amplify each other
- **Flow lines (Dashed curves)**: Continuous data exchange, learning loops, and collaborative information flow
- **Enhancement nodes (Corner circles)**: Augmented capabilities and expanded potential emerging from the integration
- **Connection lines**: The network effect of human-AI collaboration extending capabilities in all directions

The design symbolizes the transformation from separate human and AI systems to an integrated framework where both enhance each other's capabilities.

## AI Provider Configuration

The application supports multiple AI providers through a flexible configuration system:

### Supported Providers

- **OpenAI**: `https://api.openai.com/v1`
- **OpenRouter**: `https://openrouter.ai/api/v1` (access to hundreds of models)
- **Local Models**: `http://localhost:1234/v1` (e.g., LM Studio, Ollama with OpenAI compatibility)
- **Custom APIs**: Any OpenAI-compatible endpoint

### Setup

1. Click the "🔑 Configure AI API" button
2. Enter your base URL and API key
3. Click "Save Configuration"

### OpenRouter Setup

For OpenRouter (recommended for variety and cost-effectiveness):

1. Sign up at [openrouter.ai](https://openrouter.ai)
2. Get your API key from the dashboard
3. Use base URL: `https://openrouter.ai/api/v1`
4. Choose from hundreds of available models

## Development

### Prerequisites

- Node.js 18+
- npm

### Setup

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env and add your Groq API key (get free key from https://console.groq.com/keys)

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Environment Variables

The application uses environment variables for secure API key management:

1. Copy `.env.example` to `.env`
2. Get a free Groq API key from [console.groq.com/keys](https://console.groq.com/keys)
3. Add your API key to the `.env` file:

```env
VITE_GROQ_API_KEY=your_actual_groq_api_key_here
```

**Note**: The `.env` file is automatically ignored by git to keep your API keys secure.

### Project Structure

- `main.js` - Main application logic with ES6 modules
- `index.html` - HTML entry point
- `style.css` - Styling and UI components
- `package.json` - Dependencies and scripts

## Usage

### Basic Operations

- **Create Node**: Click "Add Node" or double-click empty space
- **Edit Node**: Double-click a node to edit text
- **Select Node**: Click a node to select it
- **Move Node**: Drag a selected node
- **Resize Node**: Drag the resize handles on selected nodes
- **Connect Nodes**: Ctrl/Cmd + click and drag from one node to another
- **Delete Node**: Select a node and press Delete/Backspace

### Canvas Navigation

- **Pan**: Drag empty space or use middle mouse button
- **Zoom**: Mouse wheel or zoom buttons
- **Reset View**: Click the home button (⌂)

### AI Features

1. Select a node containing your central idea
2. Click "🤖 Generate AI Ideas"
3. The AI will generate 3-5 related concepts
4. New nodes will be created and automatically connected

## Technical Details

### Dependencies

- **Vite**: Modern build tool and dev server
- **OpenAI SDK**: For AI API integration
- **ES6 Modules**: Modern JavaScript module system

### Features Implemented

- ✅ Vite-based build system
- ✅ ES6 module structure
- ✅ Multi-provider AI configuration
- ✅ OpenRouter integration
- ✅ Local model support
- ✅ Enhanced UI with modal dialogs
- ✅ Configuration persistence
- ✅ Error handling and notifications
- ✅ Provider detection and branding

### Browser Compatibility

Modern browsers with ES6 module support required.

## License

MIT License